import React, { ReactNode, useState, useEffect } from 'react';
import { Heading } from '../atoms';
import NextImage from 'next/image';
import { PortalComponent } from '../atoms/Portal';
import classNames from 'classnames';
import { ArrowLeft } from 'iconsax-react';

interface ModalProps {
    isOpen: boolean;
    onClose?: () => void;
    modalTitle?: any;
    modalTitleTransform?: string;
    children?: ReactNode;
    dataAutomation: string;
    isHideCloseBtn?: boolean;
    headerRightContent?: React.ReactNode;
    modalWidth?: string;
    modalHeight?: string;
    isShowBorder?: boolean;
    modalFooter?: React.ReactNode;
    icon?: React.ReactNode;
    isHeaderBorder?: boolean;
    childrenPt?: string;
    childrenPr?: string;
    onBackArrowBtn?: () => void;
    isModalSubTitle?: boolean;
    modalSubTitle?: string;
    subModalTitleClass?: string;
    isPaddingRequired?: boolean;
}

const Modal: React.FC<ModalProps> = ({
    isOpen,
    onClose,
    modalTitle,
    modalTitleTransform = 'capitalize',
    children,
    dataAutomation,
    isHideCloseBtn,
    headerRightContent,
    modalWidth = 'max-w-[560px]',
    modalHeight,
    modalFooter,
    isShowBorder = true,
    icon,
    isHeaderBorder = true,
    childrenPt = 'pt-4',
    childrenPr = 'pr-2',
    onBackArrowBtn,
    isModalSubTitle = false,
    modalSubTitle,
    subModalTitleClass = 'text-sm text-neutral-400 font-normal',
    isPaddingRequired = true,
}) => {
    const [isClosing, setIsClosing] = useState(false);
    const [isVisible, setIsVisible] = useState(isOpen); // Control visibility

    useEffect(() => {
        if (isOpen) {
            document.body.classList.add('overflow-hidden');
            setIsVisible(true); // Show modal
            setIsClosing(false); // Reset closing state
        } else {
            setIsClosing(true); // Start closing
            const timeout = setTimeout(() => {
                setIsVisible(false); // Hide modal after transition
            }, 300); // Match this with your CSS transition duration
            return () => clearTimeout(timeout);
        }

        return () => {
            document.body.classList.remove('overflow-hidden');
        };
    }, [isOpen]);

    const handleCloseModal = () => {
        if (onClose) onClose();
    };

    // Render modal only when it's visible
    if (!isVisible) return null;

    return (
        <div
            className={`fixed inset-0 z-50 transition-opacity ${isClosing ? 'opacity-0' : 'opacity-100'}`}
            aria-labelledby={modalTitle}
            role="dialog"
            aria-modal="true"
            aria-hidden={!isVisible}
        >
            <div
                className="fixed inset-0 bg-neutral-900 bg-opacity-40 transition-opacity"
                aria-hidden="true"
                id="modal-layer"
                data-automation={`${dataAutomation}-layer`}
            ></div>

            <div className="flex items-center justify-center h-screen">
                <div
                    className={`${modalWidth} relative transform overflow-hidden rounded-2xl bg-basic-white w-full transition-transform ${isClosing ? 'scale-90 opacity-0' : 'scale-100 opacity-100'}`}
                    role="document"
                    tabIndex={-1}
                >
                    <div className={isPaddingRequired ? 'p-8' : ''}>
                        <div
                            className={classNames(
                                isHeaderBorder &&
                                    'border-b border-neutral-100 pb-3',
                                'flex justify-between items-center gap-2.5',
                                isPaddingRequired ? '' : 'mx-8 mt-8'
                            )}
                        >
                            {icon}
                            {onBackArrowBtn && (
                                <ArrowLeft
                                    onClick={onBackArrowBtn}
                                    size="24"
                                    className="cursor-pointer text-neutral-900 mr-8"
                                />
                            )}
                            <Heading
                                type="h6"
                                className={`${modalTitleTransform} text-neutral-900 font-semibold flex-grow`}
                            >
                                {modalTitle}
                                {isModalSubTitle && (
                                    <span
                                        className={`${subModalTitleClass} block`}
                                    >
                                        {modalSubTitle}
                                    </span>
                                )}
                            </Heading>

                            {headerRightContent}

                            {!isHideCloseBtn && (
                                <button
                                    className="cursor-pointer focus:outline-none ml-2"
                                    onClick={handleCloseModal}
                                    id="close-modal-button"
                                    data-automation={`${dataAutomation}-close-modal`}
                                >
                                    <NextImage
                                        src="/images/icons/close.svg"
                                        alt="close"
                                        width={14}
                                        height={14}
                                    />
                                </button>
                            )}
                        </div>

                        <div
                            className={classNames(
                                'max-h-[70vh] light-scrollbar overflow-x-auto',
                                childrenPt,
                                childrenPr,
                                modalHeight ?? 'max-h-[calc(90vh_-_182px)]'
                            )}
                        >
                            {children}
                        </div>

                        {modalFooter && (
                            <ModalFooter isShowBorder={isShowBorder}>
                                {modalFooter}
                            </ModalFooter>
                        )}

                        <div id="portal-drawer-footer"></div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PortalComponent(Modal);

interface ModalFooterType {
    className?: string;
    children: ReactNode;
    isShowBorder?: boolean;
}

export const ModalFooter = ({
    className,
    children,
    isShowBorder = true,
}: ModalFooterType) => {
    return (
        <div
            className={classNames(
                className,
                'pt-3 flex flex-row gap-x-4 justify-end',
                {
                    'border-t border-neutral-100': isShowBorder,
                }
            )}
        >
            {children}
        </div>
    );
};

export const PortalModalFooter = PortalComponent(ModalFooter);
